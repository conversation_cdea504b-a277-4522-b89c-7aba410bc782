import os

from agent.tools_and_schemas import SearchQueryList, Reflection
from dotenv import load_dotenv
from langchain_core.messages import AIMessage
from langgraph.types import Send
from langgraph.graph import StateGraph
from langgraph.graph import START, END
from langchain_core.runnables import RunnableConfig

from agent.state import (
    OverallState,
    QueryGenerationState,
    ReflectionState,
    WebSearchState,
)
from agent.configuration import Configuration
from agent.prompts import (
    get_current_date,
    query_writer_instructions,
    web_searcher_instructions,
    reflection_instructions,
    answer_instructions,
)
from langchain_community.chat_models import ChatTongyi
from agent.utils import (
    get_research_topic,
)

# load_dotenv()
#
# if os.getenv("DASHSCOPE_API_KEY") is None:
#     raise ValueError("DASHSCOPE_API_KEY is not set")


# Nodes
def generate_query(state: OverallState, config: RunnableConfig) -> QueryGenerationState:
    """LangGraph node that generates search queries based on the User's question.

    Uses <PERSON>wen to create an optimized search queries for web research based on
    the User's question.

    Args:
        state: Current graph state containing the User's question
        config: Configuration for the runnable, including LLM provider settings

    Returns:
        Dictionary with state update, including search_query key containing the generated queries
    """
    configurable = Configuration.from_runnable_config(config)

    # check for custom initial search query count
    if state.get("initial_search_query_count") is None:
        state["initial_search_query_count"] = configurable.number_of_initial_queries

    # init Qwen
    llm = ChatTongyi(
        model=configurable.query_generator_model,
        temperature=1.0,
        max_retries=2,
        dashscope_api_key=os.getenv("DASHSCOPE_API_KEY"),
    )

    # Format the prompt
    current_date = get_current_date()
    formatted_prompt = query_writer_instructions.format(
        current_date=current_date,
        research_topic=get_research_topic(state["messages"]),
        number_queries=state["initial_search_query_count"],
    )

    # Generate the search queries - 使用普通调用而不是结构化输出
    try:
        structured_llm = llm.with_structured_output(SearchQueryList)
        result = structured_llm.invoke(formatted_prompt)
        if result and hasattr(result, 'query') and result.query:
            return {"search_query": result.query}
    except Exception as e:
        print(f"结构化输出失败，使用备用方案: {e}")

    # 备用方案：使用普通文本输出并解析
    response = llm.invoke(formatted_prompt + "\n请直接返回搜索查询列表，每行一个查询。")
    queries = [line.strip() for line in response.content.split('\n') if line.strip()]

    # 确保至少有一个查询
    if not queries:
        research_topic = get_research_topic(state["messages"])
        queries = [research_topic]

    # 限制查询数量
    max_queries = state["initial_search_query_count"]
    queries = queries[:max_queries]

    return {"search_query": queries}


def continue_to_web_research(state: QueryGenerationState):
    """LangGraph node that sends the search queries to the web research node.

    This is used to spawn n number of web research nodes, one for each search query.
    """
    return [
        Send("web_research", {"search_query": search_query, "id": int(idx)})
        for idx, search_query in enumerate(state["search_query"])
    ]


def web_research(state: WebSearchState, config: RunnableConfig) -> OverallState:
    """LangGraph node that performs web research using Qwen.

    Executes a web search simulation using Qwen to generate research content based on the search query.

    Args:
        state: Current graph state containing the search query and research loop count
        config: Configuration for the runnable, including search API settings

    Returns:
        Dictionary with state update, including sources_gathered, research_loop_count, and web_research_results
    """
    # Configure
    configurable = Configuration.from_runnable_config(config)
    formatted_prompt = web_searcher_instructions.format(
        current_date=get_current_date(),
        research_topic=state["search_query"],
    )

    # Use Qwen to generate research content
    llm = ChatTongyi(
        model=configurable.query_generator_model,
        temperature=0,
        max_retries=2,
        dashscope_api_key=os.getenv("DASHSCOPE_API_KEY"),
    )

    response = llm.invoke(formatted_prompt)

    # Create mock sources for the research
    sources_gathered = [
        {
            "label": f"Research Source {state['id']+1}",
            "short_url": f"https://example.com/source-{state['id']+1}",
            "value": f"https://example.com/detailed-source-{state['id']+1}",
        }
    ]

    return {
        "sources_gathered": sources_gathered,
        "search_query": [state["search_query"]],
        "web_research_result": [response.content],
    }


def reflection(state: OverallState, config: RunnableConfig) -> ReflectionState:
    """LangGraph node that identifies knowledge gaps and generates potential follow-up queries.

    Analyzes the current summary to identify areas for further research and generates
    potential follow-up queries. Uses structured output to extract
    the follow-up query in JSON format.

    Args:
        state: Current graph state containing the running summary and research topic
        config: Configuration for the runnable, including LLM provider settings

    Returns:
        Dictionary with state update, including search_query key containing the generated follow-up query
    """
    configurable = Configuration.from_runnable_config(config)
    # Increment the research loop count and get the reasoning model
    state["research_loop_count"] = state.get("research_loop_count", 0) + 1
    reasoning_model = state.get("reasoning_model", configurable.reflection_model)

    # Format the prompt
    current_date = get_current_date()
    formatted_prompt = reflection_instructions.format(
        current_date=current_date,
        research_topic=get_research_topic(state["messages"]),
        summaries="\n\n---\n\n".join(state["web_research_result"]),
    )
    # init Reasoning Model
    llm = ChatTongyi(
        model=reasoning_model,
        temperature=1.0,
        max_retries=2,
        dashscope_api_key=os.getenv("DASHSCOPE_API_KEY"),
    )

    # 尝试结构化输出，失败则使用备用方案
    try:
        result = llm.with_structured_output(Reflection).invoke(formatted_prompt)
        if result and hasattr(result, 'is_sufficient'):
            return {
                "is_sufficient": result.is_sufficient,
                "knowledge_gap": result.knowledge_gap,
                "follow_up_queries": result.follow_up_queries,
                "research_loop_count": state["research_loop_count"],
                "number_of_ran_queries": len(state["search_query"]),
            }
    except Exception as e:
        print(f"Reflection结构化输出失败，使用备用方案: {e}")

    # 备用方案：简单判断是否足够
    response = llm.invoke(formatted_prompt + "\n请回答：信息是否足够？如果不够，需要什么额外信息？")
    content = response.content.lower()

    # 简单的判断逻辑
    is_sufficient = "足够" in content or "sufficient" in content or "完整" in content

    return {
        "is_sufficient": is_sufficient,
        "knowledge_gap": "需要更多详细信息" if not is_sufficient else "",
        "follow_up_queries": [] if is_sufficient else [get_research_topic(state["messages"]) + " 详细信息"],
        "research_loop_count": state["research_loop_count"],
        "number_of_ran_queries": len(state["search_query"]),
    }


def evaluate_research(
    state: ReflectionState,
    config: RunnableConfig,
) -> OverallState:
    """LangGraph routing function that determines the next step in the research flow.

    Controls the research loop by deciding whether to continue gathering information
    or to finalize the summary based on the configured maximum number of research loops.

    Args:
        state: Current graph state containing the research loop count
        config: Configuration for the runnable, including max_research_loops setting

    Returns:
        String literal indicating the next node to visit ("web_research" or "finalize_summary")
    """
    configurable = Configuration.from_runnable_config(config)
    max_research_loops = (
        state.get("max_research_loops")
        if state.get("max_research_loops") is not None
        else configurable.max_research_loops
    )
    if state["is_sufficient"] or state["research_loop_count"] >= max_research_loops:
        return "finalize_answer"
    else:
        return [
            Send(
                "web_research",
                {
                    "search_query": follow_up_query,
                    "id": state["number_of_ran_queries"] + int(idx),
                },
            )
            for idx, follow_up_query in enumerate(state["follow_up_queries"])
        ]


def finalize_answer(state: OverallState, config: RunnableConfig):
    """LangGraph node that finalizes the research summary.

    Prepares the final output by deduplicating and formatting sources, then
    combining them with the running summary to create a well-structured
    research report with proper citations.

    Args:
        state: Current graph state containing the running summary and sources gathered

    Returns:
        Dictionary with state update, including running_summary key containing the formatted final summary with sources
    """
    configurable = Configuration.from_runnable_config(config)
    reasoning_model = state.get("reasoning_model") or configurable.answer_model

    # Format the prompt
    current_date = get_current_date()
    formatted_prompt = answer_instructions.format(
        current_date=current_date,
        research_topic=get_research_topic(state["messages"]),
        summaries="\n---\n\n".join(state["web_research_result"]),
    )

    # init Reasoning Model, default to Qwen Max
    llm = ChatTongyi(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        dashscope_api_key=os.getenv("DASHSCOPE_API_KEY"),
    )
    result = llm.invoke(formatted_prompt)

    # Replace the short urls with the original urls and add all used urls to the sources_gathered
    unique_sources = []
    for source in state["sources_gathered"]:
        if source["short_url"] in result.content:
            result.content = result.content.replace(
                source["short_url"], source["value"]
            )
            unique_sources.append(source)

    return {
        "messages": [AIMessage(content=result.content)],
        "sources_gathered": unique_sources,
    }


# Create our Agent Graph
builder = StateGraph(OverallState, config_schema=Configuration)

# Define the nodes we will cycle between
builder.add_node("generate_query", generate_query)
builder.add_node("web_research", web_research)
builder.add_node("reflection", reflection)
builder.add_node("finalize_answer", finalize_answer)

# Set the entrypoint as `generate_query`
# This means that this node is the first one called
builder.add_edge(START, "generate_query")
# Add conditional edge to continue with search queries in a parallel branch
builder.add_conditional_edges(
    "generate_query", continue_to_web_research, ["web_research"]
)
# Reflect on the web research
builder.add_edge("web_research", "reflection")
# Evaluate the research
builder.add_conditional_edges(
    "reflection", evaluate_research, ["web_research", "finalize_answer"]
)
# Finalize the answer
builder.add_edge("finalize_answer", END)

graph = builder.compile(name="pro-search-agent")
