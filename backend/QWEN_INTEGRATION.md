# 通义千问集成说明

本项目已成功将 Gemini 集成替换为阿里云通义千问（Qwen）。

## 主要变更

### 1. 依赖更新
- 移除了 `langchain-google-genai` 和 `google-genai`
- 添加了 `langchain-community` 和 `dashscope`

### 2. 模型配置
默认模型已更新为通义千问系列：
- **查询生成模型**: `qwen-turbo` (原 `gemini-2.0-flash`)
- **反思模型**: `qwen-plus` (原 `gemini-2.5-flash`)  
- **答案生成模型**: `qwen-max` (原 `gemini-2.5-pro`)

### 3. API 密钥
- 环境变量从 `GEMINI_API_KEY` 更改为 `DASHSCOPE_API_KEY`

## 配置步骤

### 1. 获取 API 密钥
1. 访问 [阿里云百炼平台](https://bailian.console.aliyun.com/)
2. 注册/登录账号
3. 创建应用并获取 API Key

### 2. 设置环境变量
创建 `.env` 文件并添加：
```bash
DASHSCOPE_API_KEY=your_dashscope_api_key_here
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
# 或者如果使用 uv
uv sync
```

## 测试集成

运行测试脚本验证集成是否正常：
```bash
python test_qwen_integration.py
```

## 使用示例

### 命令行使用
```bash
python examples/cli_research.py "人工智能的最新发展趋势"
```

### 自定义模型
```bash
python examples/cli_research.py "研究问题" --reasoning-model qwen-max
```

## 支持的模型

通义千问支持以下模型：
- `qwen-turbo`: 快速响应，适合简单任务
- `qwen-plus`: 平衡性能和成本
- `qwen-max`: 最强性能，适合复杂任务
- `qwen-long`: 支持长文本处理

## 注意事项

1. **搜索功能**: 由于通义千问不支持 Google Search API 的原生集成，web_research 功能已修改为基于模型知识的研究模拟
2. **引用格式**: 源引用已简化为示例格式，实际使用中可根据需要调整
3. **成本控制**: 建议根据使用场景选择合适的模型以控制成本

## 故障排除

### 常见问题

1. **API 密钥错误**
   ```
   Error: DASHSCOPE_API_KEY is not set
   ```
   解决：检查 `.env` 文件中的 API 密钥设置

2. **模型不存在**
   ```
   Error: Model not found
   ```
   解决：确认使用的模型名称正确，参考上述支持的模型列表

3. **网络连接问题**
   ```
   Error: Connection timeout
   ```
   解决：检查网络连接，确保可以访问阿里云服务

## 性能对比

| 功能 | Gemini | 通义千问 |
|------|--------|----------|
| 查询生成 | gemini-2.0-flash | qwen-turbo |
| 知识反思 | gemini-2.5-flash | qwen-plus |
| 最终答案 | gemini-2.5-pro | qwen-max |
| 搜索集成 | 原生 Google Search | 基于知识模拟 |

## 进一步优化

如需恢复真实的网络搜索功能，可以考虑：
1. 集成百度搜索 API
2. 使用 SerpAPI 等第三方搜索服务
3. 实现自定义爬虫功能
