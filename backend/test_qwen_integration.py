#!/usr/bin/env python3
"""
Test script to verify Qwen integration is working correctly.
"""

import os
from dotenv import load_dotenv
from langchain_community.chat_models import <PERSON><PERSON><PERSON><PERSON><PERSON>
from agent.tools_and_schemas import SearchQueryList

def test_qwen_connection():
    """Test basic connection to Qwen API."""
    load_dotenv()
    
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key:
        print("❌ DASHSCOPE_API_KEY not found in environment variables")
        print("Please set your DASHSCOPE_API_KEY in the .env file")
        return False
    
    try:
        # Test basic chat functionality
        llm = ChatTongyi(
            model="qwen-turbo",
            temperature=0.7,
            dashscope_api_key=api_key,
        )
        
        response = llm.invoke("Hello, please respond with 'Qwen integration successful!'")
        print(f"✅ Basic chat test: {response.content}")
        
        # Test structured output
        structured_llm = llm.with_structured_output(SearchQueryList)
        prompt = """Generate search queries for researching "artificial intelligence trends 2024".
        
        Format your response as JSON with:
        - "rationale": Brief explanation
        - "query": List of 2-3 search queries
        """
        
        result = structured_llm.invoke(prompt)
        print(f"✅ Structured output test:")
        print(f"   Rationale: {result.rationale}")
        print(f"   Queries: {result.query}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Qwen integration: {e}")
        return False

def test_configuration():
    """Test configuration loading."""
    try:
        from agent.configuration import Configuration
        config = Configuration()
        
        print(f"✅ Configuration test:")
        print(f"   Query generator model: {config.query_generator_model}")
        print(f"   Reflection model: {config.reflection_model}")
        print(f"   Answer model: {config.answer_model}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing configuration: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Qwen integration...")
    print("=" * 50)
    
    config_ok = test_configuration()
    qwen_ok = test_qwen_connection()
    
    print("=" * 50)
    if config_ok and qwen_ok:
        print("🎉 All tests passed! Qwen integration is ready.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
